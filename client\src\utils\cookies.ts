/**
 * Cookie utility functions for managing authentication tokens and other data
 */

interface CookieOptions {
	expires?: Date;
	maxAge?: number; // in seconds
	path?: string;
	domain?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
	httpOnly?: boolean; // Note: This can't be set from client-side JavaScript
}

/**
 * Set a cookie with the given name, value, and options
 */
export function setCookie(
	name: string,
	value: string,
	options: CookieOptions = {}
): void {
	const {
		expires,
		maxAge,
		path = "/",
		domain,
		secure = window.location.protocol === "https:",
		sameSite = "lax",
	} = options;

	let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

	if (expires) {
		cookieString += `; expires=${expires.toUTCString()}`;
	}

	if (maxAge !== undefined) {
		cookieString += `; max-age=${maxAge}`;
	}

	cookieString += `; path=${path}`;

	if (domain) {
		cookieString += `; domain=${domain}`;
	}

	if (secure) {
		cookieString += "; secure";
	}

	cookieString += `; samesite=${sameSite}`;

	document.cookie = cookieString;
}

/**
 * Get a cookie value by name
 */
export function getCookie(name: string): string | null {
	const nameEQ = encodeURIComponent(name) + "=";
	const cookies = document.cookie.split(";");

	for (let cookie of cookies) {
		cookie = cookie.trim();
		if (cookie.indexOf(nameEQ) === 0) {
			return decodeURIComponent(cookie.substring(nameEQ.length));
		}
	}

	return null;
}

/**
 * Delete a cookie by name
 */
export function deleteCookie(
	name: string,
	path: string = "/",
	domain?: string
): void {
	let cookieString = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;

	if (domain) {
		cookieString += `; domain=${domain}`;
	}

	document.cookie = cookieString;
}

/**
 * Check if a cookie exists
 */
export function hasCookie(name: string): boolean {
	return getCookie(name) !== null;
}

/**
 * Get all cookies as an object
 */
export function getAllCookies(): Record<string, string> {
	const cookies: Record<string, string> = {};
	const cookieArray = document.cookie.split(";");

	for (let cookie of cookieArray) {
		cookie = cookie.trim();
		if (cookie) {
			const [name, value] = cookie.split("=");
			if (name && value) {
				cookies[decodeURIComponent(name)] = decodeURIComponent(value);
			}
		}
	}

	return cookies;
}

// Token-specific cookie utilities
const TOKEN_COOKIE_NAME = "auth_token";
const LOGGED_OUT_COOKIE_NAME = "logged_out";
const TIMESTAMP_COOKIE_NAME = "logout_timestamp";

/**
 * Set the authentication token in a cookie
 */
export function setAuthToken(token: string): void {
	// Set token to expire in 7 days (adjust as needed)
	const expires = new Date();
	expires.setDate(expires.getDate() + 7);

	setCookie(TOKEN_COOKIE_NAME, token, {
		expires,
		secure: window.location.protocol === "https:",
		sameSite: "lax", // Changed from 'strict' to 'lax' for better compatibility
		path: "/",
	});
}

/**
 * Get the authentication token from cookies
 */
export function getAuthToken(): string | null {
	return getCookie(TOKEN_COOKIE_NAME);
}

/**
 * Remove the authentication token cookie
 */
export function removeAuthToken(): void {
	deleteCookie(TOKEN_COOKIE_NAME);
}

/**
 * Set logged out status in cookie
 */
export function setLoggedOutStatus(timestamp: string): void {
	setCookie(LOGGED_OUT_COOKIE_NAME, "true", { path: "/" });
	setCookie(TIMESTAMP_COOKIE_NAME, timestamp, { path: "/" });
}

/**
 * Get logged out status from cookie
 */
export function getLoggedOutStatus(): {
	loggedOut: boolean;
	timestamp: string | null;
} {
	const loggedOut = getCookie(LOGGED_OUT_COOKIE_NAME) === "true";
	const timestamp = getCookie(TIMESTAMP_COOKIE_NAME);
	return { loggedOut, timestamp };
}

/**
 * Clear logged out status cookies
 */
export function clearLoggedOutStatus(): void {
	deleteCookie(LOGGED_OUT_COOKIE_NAME);
	deleteCookie(TIMESTAMP_COOKIE_NAME);
}
