import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import {
	TextInput,
	PasswordInput,
	Paper,
	Button,
	Stack,
	Center,
	Text,
	Image,
	Modal,
	Title,
} from "@mantine/core";
import { IconX, Icon<PERSON>heck } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import validator from "validator";
import { ADMIN_EMAIL, ADMIN_PHONE, APP_NAME } from "../constants";
import { getLoggedOutStatus, clearLoggedOutStatus } from "../utils/cookies";

export default function Login() {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [loading, setLoading] = useState(false);
	const [emailError, setEmailError] = useState("");
	const navigate = useNavigate();
	const { login } = useAuth();
	const [showAdminModal, setShowAdminModal] = useState<boolean>(false);

	const validateEmail = (email: string): boolean => {
		const trimmnedEmail = email.trim();

		if (!trimmnedEmail) {
			setEmailError("Email is required");
			return false;
		}
		if (!validator.isEmail(trimmnedEmail)) {
			setEmailError("Please enter a valid email address");
			return false;
		}
		setEmailError("");
		return true;
	};

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		if (!validateEmail(email)) {
			return;
		}

		if (!password) {
			notifications.show({
				title: "Validation Error",
				message: "Password is required",
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		setLoading(true);
		try {
			const loggedInUser = await login(
				email.trim().toLowerCase(),
				password
			);
			notifications.show({
				title: "Welcome back",
				message: "You've successfully logged in.",
				color: "green",
				icon: <IconCheck />,
			});
			if (loggedInUser && loggedInUser.role === 3) {
				navigate("/profile");
			} else {
				navigate("/users");
			}
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Login failed",
					message:
						error.response?.data?.message ||
						"User not found or invalid credentials",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Login failed",
					message: "An unexpected error occurred",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};

	setTimeout(() => {
		const { loggedOut, timestamp } = getLoggedOutStatus();
		if (timestamp) {
			const timeDiff = Date.now() - Number(timestamp);
			console.log(timeDiff);
			if (timeDiff >= 10_000) {
				clearLoggedOutStatus();
			}
		}
		if (loggedOut) {
			notifications.show({
				title: "Session Expired",
				message: "Your session has expired. Please log in again.",
				color: "red",
				icon: <IconCheck />,
			});
			clearLoggedOutStatus();
		}
	}, 100);

	return (
		<Center mih="100vh" bg="gray.0">
			<Paper withBorder shadow="md" radius="md" p="xl" w={360}>
				<Stack gap={0} align="center">
					<Image
						src="/assets/SMLogoTransparent.png"
						alt="SM"
						w={60}
						h={60}
						fit="contain"
					/>
					<Title order={1} fw={600}>
						{APP_NAME}
					</Title>
				</Stack>

				<br />

				<form onSubmit={handleSubmit}>
					<Stack gap="md" mt="md">
						<TextInput
							label="Email"
							placeholder="<EMAIL>"
							required
							value={email}
							onChange={e => {
								setEmail(e.target.value);
								if (emailError) validateEmail(e.target.value);
							}}
							error={emailError}
							disabled={loading}
						/>
						<Stack gap="xs">
							<PasswordInput
								label="Password"
								placeholder="Your password"
								required
								value={password}
								onChange={e => setPassword(e.target.value)}
								disabled={loading}
							/>

							<Text size="sm" c="dimmed" ta="right">
								<Link
									to="/forgot-password"
									style={{
										textDecoration: "none",
										color: "var(--mantine-color-blue-6)",
									}}
								>
									Forgot your password?
								</Link>
							</Text>
						</Stack>

						<Button
							type="submit"
							loading={loading}
							fullWidth
							size="md"
						>
							{loading ? "Logging in..." : "Log in"}
						</Button>
						<Text
							ta="center"
							style={{ cursor: "pointer" }}
							size="sm"
							c="var(--mantine-color-blue-6)"
							onClick={() => setShowAdminModal(true)}
						>
							Need Assistance?
						</Text>
					</Stack>
				</form>
			</Paper>

			<Modal
				centered
				opened={showAdminModal}
				onClose={() => setShowAdminModal(false)}
				title="Need Assistance?"
				trapFocus={false}
			>
				<Text mb="sm">
					If you need any assistance with accessing or using the
					platform, please reach out to us.
				</Text>
				Email:
				<Text c="blue">
					<a href={`mailto:${ADMIN_EMAIL}`}>{ADMIN_EMAIL}</a>
				</Text>
				Phone:
				<Text c="blue">
					<a href={`tel:${ADMIN_PHONE}`}>{ADMIN_PHONE}</a>
				</Text>
				<Text mt="sm">
					We'll get back to you quickly to help you access your
					account.
				</Text>
			</Modal>
		</Center>
	);
}
