import { createContext, useState, useContext, useEffect } from "react";
import type { ReactNode } from "react";
import { apiClient } from "../config/axios";
import type { AuthContextType, Session } from "../types";
import { getAuthToken, setAuthToken, removeAuthToken } from "../utils/cookies";

const AuthContext = createContext<AuthContextType | null>(null);

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
	const context = useContext(AuthContext);
	if (!context) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};

interface AuthProviderProps {
	children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
	const [user, setUser] = useState<Session | null>(null);
	const [loading, setLoading] = useState(true);
	const [isUpdatingUser, setIsUpdatingUser] = useState(false);
	const [isSwitchingAdminPanel, setIsSwitchingAdminPanel] = useState(false);
	useEffect(() => {
		const token = getAuthToken();
		if (token) {
			apiClient.defaults.headers.common["Authorization"] =
				`Bearer ${token}`;
			fetchUser();
		} else {
			setLoading(false);
		}
	}, []);
	const fetchUser = async (isUpdate = false): Promise<Session | null> => {
		try {
			if (isUpdate) {
				setIsUpdatingUser(true);
			}
			const response = await apiClient.get<Session>("/api/auth/session");
			setUser(response.data);
			return response.data;
		} catch (error) {
			console.log(error);
			removeAuthToken();
			return null;
		} finally {
			setLoading(false);
			if (isUpdate) {
				setIsUpdatingUser(false);
			}
		}
	};

	const login = async (email: string, password: string): Promise<Session> => {
		const response = await apiClient.post<{ token: string }>(
			"/api/auth/login",
			{ email, password }
		);
		const { token } = response.data;
		setAuthToken(token);
		apiClient.defaults.headers.common["Authorization"] = `Bearer ${token}`;
		const fetchedUser = await fetchUser();
		return fetchedUser as Session;
	};

	const logout = () => {
		removeAuthToken();
		setUser(null);
	};

	const switchAdminPanel = async (): Promise<{ message: string }> => {
		try {
			setIsSwitchingAdminPanel(true);
			const response = await apiClient.post(
				"/api/users/changeAdminPanelView"
			);
			await fetchUser(true);
			return response.data;
		} finally {
			setIsSwitchingAdminPanel(false);
		}
	};
	const value: AuthContextType = {
		user,
		loading,
		isUpdatingUser,
		isSwitchingAdminPanel,
		login,
		logout,
		setUser,
		fetchUser,
		switchAdminPanel,
	};

	return (
		<AuthContext.Provider value={value}>
			{!loading && children}
		</AuthContext.Provider>
	);
}
