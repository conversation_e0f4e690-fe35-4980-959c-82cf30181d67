import axios, { AxiosError } from "axios";

// Create an axios instance for our API
export const apiClient = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	withCredentials: true,
});

apiClient.interceptors.response.use(
	response => {
		// Check if the response contains a new token
		const newToken = response.headers["x-new-token"];
		if (newToken) {
			localStorage.setItem("token", newToken);
			apiClient.defaults.headers.common["Authorization"] =
				`Bearer ${newToken}`;
		}
		return response;
	},
	error => {
		if (error instanceof AxiosError && error.response?.status === 401) {
			window.location.assign("/login");
			localStorage.setItem("loggedOut", "true");
			localStorage.setItem("timeStamp", Date.now().toString());
		}
		return Promise.reject(error);
	}
);

// Set up interceptor for authentication token
apiClient.interceptors.request.use(config => {
	const token = localStorage.getItem("token");
	if (token) {
		config.headers.Authorization = `Bearer ${token}`;
	}
	return config;
});

// Default axios instance for other requests
export const createClient = (baseURL: string) => {
	return axios.create({
		baseURL,
		withCredentials: true,
	});
};

export default apiClient;
