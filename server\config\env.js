import { config } from "dotenv";
import { z } from "zod";

config();

const envSchema = z.object({
	NODE_ENV: z
		.enum(["development", "test", "production"])
		.default("production"),

	MONGO_URI: z.string().default("mongodb://localhost:27017/360-DB"),

	PORT: z.coerce.number().int().positive().default(5000),

	FRONTEND_URL: z.string().url(),

	ACCESS_TOKEN_SECRET: z.string().min(1).default("ACCESS_TOKEN"),

	ACCESS_TOKEN_EXPIRE_TIME: z.string().default("1d"),

	EMAIL_HOST: z.string().min(1),

	EMAIL_PORT: z.coerce.number().int().positive().optional(),

	EMAIL_USER: z.string().nonempty(),

	EMAIL_PASS: z.string().min(1),

	EMAIL_FROM: z.string().email(),

	RETRY_NOTIFICATION_EMAIL: z
		.string()
		.email()
		.default("<EMAIL>"),

	OPENAI_API_KEY: z.string().min(1),

	S3_BUCKET: z.string().min(1),

	S3_ACCESS_KEY: z.string().min(1),

	S3_SECRET_ACCESS_KEY: z.string().min(1),

	S3_REGION: z.string().min(1),
});

const parsedEnv = envSchema.safeParse(process.env);
if (parsedEnv.error) {
	console.error(JSON.stringify(parsedEnv.error, null, 2));
	throw new Error("Env is not properly set upped", parsedEnv.error);
}

export const APP_CONFIG = {
	...parsedEnv.data,
	isDevelopment: parsedEnv.data.NODE_ENV === "development",
	isProduction: parsedEnv.data.NODE_ENV === "production",
	isTest: parsedEnv.data.NODE_ENV === "test",
};
